import express from 'express';
import dotenv from 'dotenv';
import path from 'path';
import { EmbeddingService } from './embeddings/embedding.service';
import { VectorDbService } from './storage/vector-db.service';
import { RagService, RagConfig } from './retrieval/retriever.service';
import { IndexerService } from './indexing/indexer.service';
import { logger } from './utils/logger';

// Load environment variables from multiple locations
// First try the root .env.local, then the package-specific .env.local
dotenv.config({ path: path.join(process.cwd(), '.env.local') });
dotenv.config({ path: path.join(__dirname, '..', '.env.local') });

// Configuration for server mode
const RAG_SERVER_MODE = process.env.RAG_SERVER_MODE || 'standalone'; // 'standalone' | 'library'
const RAG_SYSTEM_PORT = parseInt(process.env.RAG_SYSTEM_PORT || '6000', 10);

// Environment validation functions
function getRequiredEnvVar(name: string, description?: string): string {
  const value = process.env[name];
  if (!value || value.trim() === '') {
    const desc = description ? ` (${description})` : '';
    throw new Error(
      `Required environment variable ${name}${desc} is missing or empty.\n` +
      'Please set this variable in your .env file.'
    );
  }
  return value.trim();
}

function getRequiredEnvNumber(name: string, description?: string): number {
  const value = getRequiredEnvVar(name, description);
  const num = parseInt(value, 10);
  if (isNaN(num)) {
    const desc = description ? ` (${description})` : '';
    throw new Error(
      `Environment variable ${name}${desc} must be a valid number, got: ${value}`
    );
  }
  return num;
}

function getRequiredEnvFloat(name: string, description?: string): number {
  const value = getRequiredEnvVar(name, description);
  const num = parseFloat(value);
  if (isNaN(num)) {
    const desc = description ? ` (${description})` : '';
    throw new Error(
      `Environment variable ${name}${desc} must be a valid number, got: ${value}`
    );
  }
  return num;
}

// Create configuration with strict validation
const ragConfig: RagConfig = {
  embedding: {
    provider: getRequiredEnvVar('EMBEDDING_PROVIDER', 'Embedding provider type') as 'openai' | 'vllm',
    apiKey: getRequiredEnvVar('EMBEDDING_API_KEY', 'Embedding API key'),
    model: getRequiredEnvVar('EMBEDDING_MODEL', 'Embedding model name'),
    baseUrl: getRequiredEnvVar('EMBEDDING_BASE_URL', 'Embedding base URL'),
    dimensions: getRequiredEnvNumber('EMBEDDING_DIMENSIONS', 'Embedding dimensions'),
    batchSize: getRequiredEnvNumber('EMBEDDING_BATCH_SIZE', 'Embedding batch size'),
    timeout: 30000,
  },
  vectorDb: {
    apiKey: getRequiredEnvVar('PINECONE_API_KEY', 'Pinecone API key'),
    indexName: getRequiredEnvVar('PINECONE_INDEX_NAME', 'Pinecone index name'),
    dimension: getRequiredEnvNumber('PINECONE_DIMENSION', 'Pinecone vector dimension'),
    metric: 'cosine',
  },
  retrieval: {
    topK: getRequiredEnvNumber('RAG_MAX_RESULTS', 'RAG max results'),
    similarityThreshold: getRequiredEnvFloat('RAG_SIMILARITY_THRESHOLD', 'RAG similarity threshold'),
    maxContextLength: getRequiredEnvNumber('RAG_CONTEXT_WINDOW', 'RAG context window size'),
    enableReranking: process.env.RAG_ENABLE_RERANKING === 'true',
  },
};

// Initialize services
const embeddingService = new EmbeddingService(ragConfig.embedding);
const vectorDbService = new VectorDbService(ragConfig.vectorDb);
const ragService = new RagService(ragConfig);
const indexerService = new IndexerService(vectorDbService, embeddingService);

// Create Express app (only in standalone mode)
const app = express();
const port = process.env.PORT || RAG_SYSTEM_PORT;

// Middleware
app.use(express.json());

// API endpoints
app.post('/api/query', async (req, res) => {
  try {
    const { query, topK = 5, category } = req.body;

    if (!query) {
      return res.status(400).json({ error: 'Query is required' });
    }

    logger.info(`Processing query: ${query}`);
    const results = await ragService.searchFaqs(query, { topK, category });

    return res.json({ results });
  } catch (error) {
    logger.error('Error processing query:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while processing the query';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.post('/api/index', async (req, res) => {
  try {
    const { documents } = req.body;

    if (!documents || !Array.isArray(documents)) {
      return res.status(400).json({ error: 'Documents array is required' });
    }

    logger.info(`Indexing ${documents.length} documents`);
    const result = await indexerService.indexDocuments(documents);

    return res.json({ success: true, count: result.count });
  } catch (error) {
    logger.error('Error indexing documents:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while indexing documents';
    return res.status(500).json({
      error: errorMessage
    });
  }
});

app.post('/api/initialize', async (req, res) => {
  try {
    const { faqData = [] } = req.body;

    logger.info(`Initializing RAG system with ${faqData.length} FAQ entries`);

    if (faqData.length > 0) {
      // Convert FAQ entries to documents for indexing
      const documents = faqData.map((faq: any) => ({
        id: faq.id || `faq-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        content: `Question: ${faq.question}\nAnswer: ${faq.answer}`,
        metadata: {
          source: 'faq',
          title: faq.question,
          category: faq.category || 'General',
          question: faq.question,
          answer: faq.answer,
          tags: faq.tags || [],
          createdAt: faq.createdAt || new Date().toISOString(),
          updatedAt: faq.updatedAt || new Date().toISOString(),
          lastUpdated: new Date().toISOString(),
          ...faq.metadata
        }
      }));

      // Index the documents
      const result = await indexerService.indexDocuments(documents);
      logger.info(`Successfully initialized RAG system with ${result.count} documents`);
    } else {
      logger.info('RAG system initialized with empty FAQ data');
    }

    return res.json({
      success: true,
      message: 'RAG system initialized successfully',
      count: faqData.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    logger.error('Error initializing RAG system:', error);
    const errorMessage = error instanceof Error ? error.message : 'An error occurred while initializing the RAG system';
    return res.status(500).json({
      success: false,
      error: errorMessage
    });
  }
});

app.get('/health', (_req, res) => {
  res.json({ status: 'ok' });
});

// Start the server only in standalone mode
if (RAG_SERVER_MODE === 'standalone' && process.env.NODE_ENV !== 'test') {
  app.listen(port, () => {
    logger.info(`RAG System running in standalone mode on port ${port}`);
  });
} else if (RAG_SERVER_MODE === 'library') {
  logger.info('RAG System initialized in library mode (no server started)');
}

export {
  embeddingService,
  vectorDbService,
  ragService,
  indexerService
};

// Export types and classes for external use
export { RagService } from './retrieval/retriever.service';
export type { RagConfig, RetrievalResult } from './retrieval/retriever.service';
export { EmbeddingService } from './embeddings/embedding.service';
export type { EmbeddingConfig } from './embeddings/embedding.service';
export { VectorDbService } from './storage/vector-db.service';
export type { VectorDbConfig } from './storage/vector-db.service';
export { dataProcessor } from './indexing/data-processor';
export * from './types/document';
